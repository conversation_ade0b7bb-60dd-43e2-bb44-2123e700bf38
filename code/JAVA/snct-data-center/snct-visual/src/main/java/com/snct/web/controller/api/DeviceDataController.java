package com.snct.web.controller.api;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.utils.StringUtils;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.service.RealtimeService;
import com.snct.service.device.AttitudeService;
import com.snct.service.device.AwsService;
import com.snct.service.device.GpsService;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.impl.DeviceServiceImpl;
import com.snct.system.service.impl.ShipServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;

/**
 * 设备数据
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
//@CrossOrigin
@RestController
@RequestMapping("/api/ship")
public class DeviceDataController extends BaseController {

    @Autowired
    private ShipServiceImpl shipService;

    @Autowired
    private DeviceServiceImpl deviceService;

    @Autowired
    private RealtimeService realtimeService;

    @Autowired
    private GpsService gpsService;

    @Autowired
    private AwsService awsService;

    @Autowired
    private AttitudeService attitudeService;


    /**
     * 获取数据
     */
    @GetMapping("/list")
    public AjaxResult list(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {
        if (StringUtils.isBlank(sn) || StringUtils.isBlank(deviceCode)) {
            return AjaxResult.error("获取失败，缺少参数");
        }

        Device device = deviceService.selectDeviceBySnAndCode(sn, deviceCode);
        if (device == null) {
            return AjaxResult.error("获取失败，没有该设备");
        }
        return AjaxResult.error("获取失败，没有该类型设备");
    }


    @GetMapping("/test/getLatestDataByType")
    public AjaxResult getLatestData(String sn, Long type) {
        return AjaxResult.success(realtimeService.getLatestData(sn, type));
    }

    @GetMapping("/test/getLatestDataByCode")
    public AjaxResult getLatestData(String sn, String deviceCode) {
        return AjaxResult.success(realtimeService.getLatestData(sn, deviceCode));
    }

    @GetMapping("/test/getDataList")
    public AjaxResult getDataList(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {
        //return AjaxResult.success(gpsService.getGpsData(sn,deviceCode, interval, startTime, endTime));
        //return AjaxResult.success(awsService.queryByTime(sn,deviceCode, interval, startTime, endTime));
        return AjaxResult.success(attitudeService.queryByTime(sn, deviceCode, interval, startTime, endTime));
    }

    @GetMapping("/test/trackList")
    public AjaxResult getTrackList(Long shipId, Long startTime, Long endTime, Integer interval) {

        Ship ship = shipService.selectShipByShipId(shipId);

        if (ship == null) {
            return null;
        }

        String sn = ship.getSn();

        // 根据 sn 和设备类型查询设备列表
        List<Device> devices = deviceService.selectSimpleDeviceListBySnAndType(sn,
                Long.valueOf(DeviceTypeEnum.GPS.getValue()));

        // 检查设备列表是否为空
        if (devices == null || devices.isEmpty()) {
            return null;
        }

        // 按优先级排序，取优先级最高的设备（cost 值最大的）
        Device selectedDevice = devices.stream()
                .filter(device -> device.getCost() != null)
                .max(Comparator.comparing(Device::getCost))
                .orElse(devices.get(0));
        List<List<Double>> gpsData = gpsService.getGpsData(sn, selectedDevice.getCode(), interval, startTime, endTime);

        return AjaxResult.success(gpsData);
    }


    @GetMapping("/test/historyData")
    public AjaxResult getHistoryData(Long shipId, Long deviceId, Long startTime, Long endTime, Integer interval) {

        Ship ship = shipService.selectShipByShipId(shipId);

        Device device = deviceService.selectDeviceById(deviceId);
        if (ship == null) {
            return null;
        }
        if (device == null) {
            return null;
        }

        String sn = ship.getSn();

        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            return AjaxResult.success(gpsService.getGpsData(sn, device.getCode(), interval, startTime, endTime));
        }
        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            return AjaxResult.success(awsService.queryByTime(sn, device.getCode(), interval, startTime, endTime));
        }
        if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            return AjaxResult.success(attitudeService.queryByTime(sn, device.getCode(), interval, startTime, endTime));
        } else {
            return AjaxResult.error("获取失败，没有该类型设备");
        }
    }





    //@GetMapping("/reatData")
    //public AjaxResult getRealtimeData(String sn, String deviceCode) {
    //    //realtimeService.get
    //    return AjaxResult.success(realtimeService.getReidsNewData(sn, deviceCode));
    //}

}
