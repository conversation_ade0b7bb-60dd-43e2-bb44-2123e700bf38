package com.snct.web.controller.api;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.service.RealtimeService;
import com.snct.service.device.AttitudeService;
import com.snct.service.device.AwsService;
import com.snct.service.device.GpsService;
import com.snct.system.domain.*;
import com.snct.system.service.IBuCruiseService;
import com.snct.system.service.IBuDeviceConfigService;
import com.snct.system.service.IBuShipWarningService;
import com.snct.system.service.IShipService;
import com.snct.system.service.impl.DeviceServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 航次Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/api/ship")
public class BuCruiseController extends BaseController
{
    @Autowired
    private IBuCruiseService buCruiseService;
    @Autowired
    private IShipService shipService;
    @Autowired
    private DeviceServiceImpl deviceService;
    @Autowired
    private RealtimeService realtimeService;
    @Autowired
    private GpsService gpsService;
    @Autowired
    private AwsService awsService;
    @Autowired
    private AttitudeService attitudeService;
    @Autowired
    private IBuShipWarningService buShipWarningService;
    @Autowired
    private IBuDeviceConfigService buDeviceConfigService;
    /**
     * 查询航次列表
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:list')")
    @GetMapping("/cruiseList/{shipId}")
    public AjaxResult list(@PathVariable("shipId") String shipId)
    {
        if(shipId==null || "".equals(shipId)|| "null".equals(shipId)){
            return AjaxResult.error("参数错误！");
        }
        Ship ship = shipService.selectShipByShipId(Long.parseLong(shipId));
        if(ship==null){return AjaxResult.error("参数错误！");}
        BuCruise buCruise = new BuCruise();
        buCruise.setSn(ship.getSn());
        List<BuCruise> list = buCruiseService.selectBuCruiseList(buCruise);
        List relist = new ArrayList();
        for(BuCruise cruise : list){
            Map map = new HashMap();
            map.put("cruiseId",cruise.getCruiseId());
            map.put("code",cruise.getCode());
            relist.add(map);
        }
        return AjaxResult.success(relist);
    }

    /**
     * 获取航次详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:query')")
    @GetMapping(value = "/cruiseInfo/{cruiseId}")
    public AjaxResult getInfo(@PathVariable("cruiseId") Long cruiseId)
    {
        BuCruise cruise = buCruiseService.selectBuCruiseByCruiseId(cruiseId);
        if(cruise==null){return AjaxResult.error("参数错误！");}
        Map map = new HashMap();
        map.put("cruiseId",cruise.getCruiseId());
        map.put("code",cruise.getCode());
        map.put("stime",cruise.getStartTime());
        map.put("etime",cruise.getFinishTime());

          //测试
//        BuDeviceConfig buDeviceConfig = new BuDeviceConfig();
//        buDeviceConfig.setConfigKey("asda");
//        buDeviceConfig.setDeviceId(433l);
//        BuDeviceConfig config = buDeviceConfigService.selectBydeviceIdAndKey(buDeviceConfig);

        return success(map);
    }


    /**
     * 设备历史数据列表接口
     */
    //@PreAuthorize("@ss.hasPermi('business:cruise:query')")
    //@GetMapping(value = "/historyData")
    //public AjaxResult historyData(Long shipId, Long deviceId,
    //          @RequestParam (value = "stime",required = false) Long stime,@RequestParam (value = "etime",required = false) Long etime)
    //{
    //    System.out.println("参数#1##:"+shipId+"#######"+deviceId+"#######"+stime+"#######"+etime);
    //
    //    Ship ship = shipService.selectShipByShipId(shipId);
    //
    //    Device device = deviceService.selectDeviceById(deviceId);
    //    if (ship == null) {
    //        return null;
    //    }
    //    if (device == null) {
    //        return null;
    //    }
    //
    //    String sn = ship.getSn();
    //
    //    if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
    //        return AjaxResult.success(gpsService.getGpsData(sn, device.getCode(), interval, startTime, endTime));
    //    }
    //    if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
    //        return AjaxResult.success(awsService.queryByTime(sn, device.getCode(), interval, startTime, endTime));
    //    }
    //    if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
    //        return AjaxResult.success(attitudeService.queryByTime(sn, device.getCode(), interval, startTime, endTime));
    //    } else {
    //        return AjaxResult.error("获取失败，没有该类型设备");
    //    }
    //
    //    return success("接口未实现");
    //}

    /**
     * 设备历史数据列表接口
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:query')")
    @GetMapping(value = "/warningData")
    public AjaxResult warningData(@RequestParam ("shipId") String shipId,
               @RequestParam (value = "stime",required = false) String stime,@RequestParam (value = "etime",required = false) String etime)
    {
        System.out.println("参数#2##:"+shipId+"#######"+stime+"#######"+etime);
        if(shipId==null || "".equals(shipId)|| "null".equals(shipId)){
            return AjaxResult.error("参数错误！");
        }
        Ship ship = shipService.selectShipByShipId(Long.parseLong(shipId));
        if(ship==null){return AjaxResult.error("参数错误！");}
        BuShipWarning buShipWarning = new BuShipWarning();
        buShipWarning.setSn(ship.getSn());
        if(( stime+"" ).length()>9 ){
            buShipWarning.setStime(stime);
        }
        if(( etime+"" ).length()>9 ){
            buShipWarning.setEtime(etime);
        }
        List<BuShipWarning> list = buShipWarningService.selectBuShipWarningList(buShipWarning);
        return success(list);
    }


}
